package com.tripudiotech.migration.service;

import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.migration.entity.FileImportError;
import com.tripudiotech.migration.repository.FileImportErrorRepository;
import io.smallrye.mutiny.Uni;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileImportErrorServiceTest {

    @Mock
    private FileImportErrorRepository fileImportErrorRepository;

    @Mock
    private FileImportService fileImportService;

    @Mock
    private ConverterService converterService;

    @InjectMocks
    private FileImportErrorService fileImportErrorService;

    @Test
    void testInsert_EmptyList_ShouldReturnVoid() {
        // Given
        List<FileImportError> emptyList = new ArrayList<>();

        // When
        Uni<Void> result = fileImportErrorService.insert(emptyList);

        // Then
        assertNotNull(result);
        verifyNoInteractions(fileImportErrorRepository);
    }

    @Test
    void testInsert_ServiceStructure() {
        // This test validates that the service is properly structured
        // and methods exist without executing reactive operations

        // Verify the service is properly injected
        assertNotNull(fileImportErrorService);
        assertNotNull(fileImportErrorRepository);
        assertNotNull(fileImportService);
        assertNotNull(converterService);

        // Test that empty list handling works (this doesn't trigger reactive operations)
        Uni<Void> emptyResult = fileImportErrorService.insert(new ArrayList<>());
        assertNotNull(emptyResult);

        // Verify no interactions with repository for empty list
        verifyNoInteractions(fileImportErrorRepository);
    }

    @Test
    void testCountByFileImportId() {
        // Given
        Long fileImportId = 123L;
        when(fileImportErrorRepository.count("fileImportId = ?1", fileImportId))
                .thenReturn(Uni.createFrom().item(10L));

        // When
        Uni<Long> result = fileImportErrorService.countByFileImportId(fileImportId);

        // Then
        assertNotNull(result);
        verify(fileImportErrorRepository).count("fileImportId = ?1", fileImportId);
    }

    @Test
    void testCountByFileImportIdAndBatchNumber() {
        // Given
        Long fileImportId = 123L;
        Integer batchNumber = 1;
        when(fileImportErrorRepository.count("fileImportId = ?1 and batchNumber = ?2", fileImportId, batchNumber))
                .thenReturn(Uni.createFrom().item(5L));

        // When
        Uni<Long> result = fileImportErrorService.countByFileImportIdAndBatchNumber(fileImportId, batchNumber);

        // Then
        assertNotNull(result);
        verify(fileImportErrorRepository).count("fileImportId = ?1 and batchNumber = ?2", fileImportId, batchNumber);
    }

    private List<FileImportError> createTestErrors(int count) {
        List<FileImportError> errors = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            FileImportError error = FileImportError.builder()
                    .tenantId("test-tenant")
                    .fileImportId(123L)
                    .rowNumber(i)
                    .errorMsg("Test error " + i)
                    .requestBody("{\"test\": \"data\"}")
                    .responseBody("{\"error\": \"test error\"}")
                    .batchNumber(1)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            errors.add(error);
        }
        return errors;
    }
}
