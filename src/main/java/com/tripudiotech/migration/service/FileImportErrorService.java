/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.google.common.collect.Lists;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.response.FileImportErrorResponse;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.entity.FileImportError;
import com.tripudiotech.migration.repository.FileImportErrorRepository;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.tripudiotech.base.constant.RequestConstants.MAX_LIMIT;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileImportErrorService {

    @Inject
    FileImportErrorRepository fileImportErrorRepository;

    @Inject
    FileImportService fileImportService;

    @Inject
    ConverterService converterService;

    public Uni<FileImportError> insert(
            @NonNull FileImportError fileImportValidationError
    ) {
        return Panache.withTransaction(
                () -> fileImportErrorRepository
                        .persist(fileImportValidationError)
        );
    }

    public Uni<PageResponse<FileImportErrorResponse>> getFileImportErrorsByFileId(
            @NonNull String tenantId,
            @NonNull String requestedBy,
            @NonNull Long fileId,
            @NonNull Integer offset,
            @NonNull Integer limit,
            UserInformation userInformation) {
        return fileImportService.getFileImport(tenantId, requestedBy, fileId, userInformation)
                .flatMap(fileImportResponse -> {
                    boolean isValidStatusToHaveValidationFailedRecords =
                            Set.of(Status.COMPLETED, Status.ERROR)
                                    .contains(
                                            Status.valueOf(fileImportResponse.getFileImportStatus())
                                    );
                    if (!isValidStatusToHaveValidationFailedRecords) {
                        log.info(
                                "FileImport status does not have validation error. FileId: {}, CurrentStatus: {}",
                                fileId,
                                fileImportResponse.getFileImportStatus()
                        );
                        return Uni.createFrom().item(
                                new PageResponse<>(
                                        Collections.emptyList(),
                                        new PageInfo(0, 0, 0)
                                )
                        );
                    }
                    return fileImportErrorRepository.getFileImportErrors(
                            tenantId,
                            fileId,
                            offset,
                            limit
                    ).map(tupleResult -> {
                        Long totalRecords = tupleResult.getItem1();
                        List<FileImportErrorResponse> listResult = tupleResult.getItem2()
                                .stream()
                                .map(this::mapToFileImportResponse)
                                .toList();
                        PageResponse<FileImportErrorResponse> responseData = new PageResponse<>();
                        responseData.setData(listResult);
                        responseData.setPageInfo(new PageInfo(totalRecords, limit, listResult.size()));
                        return responseData;
                    });
                });
    }

    private FileImportErrorResponse mapToFileImportResponse(
            @NonNull FileImportError fileImportError
    ) {
        return FileImportErrorResponse.builder()
                .lineNumber(fileImportError.getRowNumber())
                .errorMsg(fileImportError.getErrorMsg())
                .requestBody(
                        StringUtils.isBlank(fileImportError.getRequestBody()) ?
                                fileImportError.getRequestBody() :
                                converterService.convertStringToValue(
                                        fileImportError.getRequestBody(),
                                        Map.class
                                )
                )
                .responseBody(
                        StringUtils.isBlank(fileImportError.getResponseBody()) ?
                                fileImportError.getResponseBody() :
                                converterService.convertStringToValue(
                                        fileImportError.getResponseBody(),
                                        Map.class
                                )
                )
                .type(fileImportError.getRequestType())
                .build();
    }

    public Uni<Void> insert(
            @NonNull List<FileImportError> fileImportErrors
    ) {
        if (CollectionUtils.isEmpty(fileImportErrors)) {
            return Uni.createFrom().voidItem();
        }

        log.info("Starting to persist {} FileImportError records", fileImportErrors.size());

        List<List<FileImportError>> splits = Lists.partition(fileImportErrors, MAX_LIMIT);
        List<Uni<Void>> batchUnis = new ArrayList<>();

        // Process batches sequentially to avoid race conditions
        for (int i = 0; i < splits.size(); i++) {
            final int batchIndex = i;
            final List<FileImportError> batch = splits.get(i);

            Uni<Void> batchUni = Panache.withTransaction(
                    () -> fileImportErrorRepository.persist(batch)
            ).invoke(() -> {
                log.debug("Successfully persisted batch {} with {} error records",
                         batchIndex + 1, batch.size());
            }).onFailure().retry().withBackOff(Duration.ofMillis(100)).atMost(3)
            .onFailure().recoverWithUni(throwable -> {
                var minRow = batch.get(0).getRowNumber();
                var maxRow = batch.get(batch.size() - 1).getRowNumber();
                log.error("Failed to persist error batch {} after retries. MinRow: {}, MaxRow: {}, BatchSize: {}",
                         batchIndex + 1, minRow, maxRow, batch.size(), throwable);

                // Instead of silently ignoring, try to persist individual records
                return persistIndividualRecords(batch, batchIndex);
            });

            batchUnis.add(batchUni);
        }

        if (batchUnis.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        return Uni.combine().all().unis(batchUnis).discardItems()
                .invoke(() -> log.info("Completed persisting {} FileImportError records in {} batches",
                               fileImportErrors.size(), splits.size()));
    }

    /**
     * Fallback method to persist individual records when batch persistence fails
     */
    private Uni<Void> persistIndividualRecords(List<FileImportError> batch, int batchIndex) {
        log.warn("Attempting to persist {} records individually for batch {}", batch.size(), batchIndex + 1);

        List<Uni<Void>> individualUnis = new ArrayList<>();

        for (int i = 0; i < batch.size(); i++) {
            final int recordIndex = i;
            final FileImportError record = batch.get(i);

            Uni<Void> recordUni = Panache.withTransaction(
                    () -> fileImportErrorRepository.persist(record)
            ).replaceWithVoid()
            .invoke(() -> {
                log.debug("Successfully persisted individual error record for row {}", record.getRowNumber());
            }).onFailure().recoverWithUni(throwable -> {
                log.error("Failed to persist individual error record for row {} in batch {}, record {}: {}",
                         record.getRowNumber(), batchIndex + 1, recordIndex + 1, throwable.getMessage());
                // Even individual record failures should not stop the process
                return Uni.createFrom().voidItem();
            });

            individualUnis.add(recordUni);
        }

        return Uni.combine().all().unis(individualUnis).discardItems();
    }

    public Uni<Long> countByFileImportIdAndBatchNumber(Long fileImportId, Integer batchNumber) {
        return fileImportErrorRepository.count("fileImportId = ?1 and batchNumber = ?2", fileImportId, batchNumber);
    }

    public Uni<Long> countByFileImportId(Long fileImportId) {
        return fileImportErrorRepository.count("fileImportId = ?1", fileImportId);
    }
}
