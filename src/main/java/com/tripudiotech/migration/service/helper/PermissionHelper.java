package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.client.dto.request.GrantPermissionRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.event.embed.Permission;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.LifeCycleState;
import com.tripudiotech.datalib.model.dynamic.LifecycleSchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.rest.client.inject.RestClient;

/**
 * Helper class for handling permissions.
 */
@ApplicationScoped
@Slf4j
public class PermissionHelper {

    public static final String GRANT_PERMISSION = "GRANT_PERMISSION";
    public static final String OWNER = "OWNER";

    @Inject
    EntityService entityService;

    @RestClient
    SchemaManagerClient schemaManagerClient;

    @Inject
    ServiceExceptionHandler serviceExceptionHandler;

    /**
     * Grants permissions for an entity
     */
    public Uni<Void> grantPermission(
            String token,
            FileImport fileImport,
            CreateEntityResponse createEntityResponse,
            RowExtractedData rowExtractedData
    ) {
        try {
            Permission permission = rowExtractedData.getPermission();

            if (shouldSkipGrantPermission(permission)) {
                log.debug(
                        "Permission is empty, skip granting permission. FileId: {}, RowNumber: {}, Permission: {}",
                        fileImport.getId(),
                        rowExtractedData.getRowNumber(),
                        permission
                );

                return Uni.createFrom().voidItem();
            }

            // Skip granting permission for OWNER role
            if (OWNER.equalsIgnoreCase(permission.getRole())) {
                log.info("Ignore granting permission for OWNER role. EntityId: {}, FileImportId: {}, RowNum: {}", 
                        createEntityResponse.getId(), fileImport.getId(), rowExtractedData.getRowNumber());
                return Uni.createFrom().voidItem();
            }

            log.info(
                    "Start granting permission. EntityId: {}, FileImport: {}, Role: {}, Agent: {}",
                    createEntityResponse.getId(),
                    fileImport.getId(),
                    permission.getRole(),
                    permission.getAgentId()
            );

            return getLifecycleStartState(token, fileImport, rowExtractedData)
                    .flatMap(stateName -> applyPermission(token, fileImport, createEntityResponse, rowExtractedData, stateName));
        } catch (Exception e) {
            log.error("Unexpected error granting permission. FileId: {}, EntityId: {}",
                    fileImport.getId(), createEntityResponse.getId(), e);
            return Uni.createFrom().failure(e);
        }
    }

    /**
     * Determines if permission granting should be skipped
     */
    private boolean shouldSkipGrantPermission(Permission permission) {
        return StringUtils.isBlank(permission.getAgentId()) || StringUtils.isBlank(permission.getRole());
    }

    /**
     * Gets the start state for a lifecycle
     */
    private Uni<String> getLifecycleStartState(
            String token,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        return schemaManagerClient.getLifeCycle(
                        fileImport.getTenantId(), token, rowExtractedData.getLifecycleId())
                .map(response -> response.readEntity(LifecycleSchema.class))
                .map(lifecycleSchema ->
                        lifecycleSchema.getStates()
                                .values().stream()
                                .filter(lcState ->
                                        CollectionUtils.isNotEmpty(lcState.getCanCreate())
                                ).findFirst()
                                .map(LifeCycleState::getName)
                                .orElse("")
                )
                .flatMap(stateName -> {
                    if (StringUtils.isBlank(stateName)) {
                        FileDataPopulateException exception = new FileDataPopulateException(
                                "Cannot detect start state in lifecycle " + rowExtractedData.getLifecycleId(),
                                rowExtractedData.getRowNumber(),
                                GRANT_PERMISSION,
                                null,
                                null
                        );
                        return Uni.createFrom().failure(exception);
                    }
                    return Uni.createFrom().item(stateName);
                });
    }

    /**
     * Applies permissions to an entity
     */
    private Uni<Void> applyPermission(
            String token,
            FileImport fileImport,
            CreateEntityResponse createEntityResponse,
            RowExtractedData rowExtractedData,
            String stateName
    ) {
        Permission permission = rowExtractedData.getPermission();

        GrantPermissionRequest requestBody = GrantPermissionRequest.builder()
                .role(permission.getRole())
                .agentId(permission.getAgentId())
                .build();

        log.debug("Applying permission. EntityId: {}, State: {}, Role: {}, Agent: {}",
                createEntityResponse.getId(), stateName, permission.getRole(), permission.getAgentId());

        return entityService.grantPermission(
                        token,
                        fileImport.getTenantId(),
                        createEntityResponse.getId(),
                        stateName,
                        requestBody
                )
                .flatMap(response -> {
                    log.info("Successfully granted permission. EntityId: {}, Role: {}, Agent: {}",
                            createEntityResponse.getId(), permission.getRole(), permission.getAgentId());
                    return Uni.createFrom().voidItem();
                })
                .onFailure(Exception.class)
                .recoverWithUni(throwable -> handlePermissionFailure(
                        fileImport, rowExtractedData, throwable, requestBody));
    }

    /**
     * Handles failures during permission granting
     */
    private Uni<Void> handlePermissionFailure(
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            Throwable throwable,
            GrantPermissionRequest requestBody
    ) {
        try {
            // If it's a ServiceException, use our common handler
            if (throwable instanceof ServiceException serviceException) {
                return serviceExceptionHandler.handleServiceException(
                        fileImport,
                        rowExtractedData.getRowNumber(),
                        GRANT_PERMISSION,
                        requestBody,
                        serviceException
                )
                .flatMap(response -> Uni.createFrom().<Void>failure(throwable));
            }

            // Otherwise, use the existing approach for other types of exceptions
            FileDataPopulateException fileDataPopulateException = serviceExceptionHandler.createFileDataPopulateException(
                    GRANT_PERMISSION,
                    rowExtractedData.getRowNumber(),
                    throwable,
                    requestBody
            );
            return serviceExceptionHandler.handleFileDataPopulateException(fileImport, rowExtractedData.getRowNumber(),
                            GRANT_PERMISSION, fileDataPopulateException)
                    .flatMap(vr -> Uni.createFrom().failure(throwable));
        } catch (Exception e) {
            log.error("Failed to handle permission exception. FileId: {}, Action: {}, RowNum: {}",
                    fileImport.getId(), GRANT_PERMISSION, rowExtractedData.getRowNumber(), e);
            return Uni.createFrom().failure(throwable);
        }
    }
}
