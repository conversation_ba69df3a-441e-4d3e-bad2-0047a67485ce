# Import Error Handling Fix

## Problem Description

The AuthServiceHelper was experiencing inconsistent behavior where logs showed 10 failed import records, but the actual stored count in the database varied (sometimes 9, sometimes 10, etc.). This inconsistency was causing confusion and making it difficult to track import failures accurately.

## Root Cause Analysis

### 1. **Silent Batch Failures**
The `FileImportErrorService.insert()` method used parallel processing with silent failure recovery:

```java
splits.parallelStream().forEach(batch -> {
    var savedBatchResult = Panache.withTransaction(
            () -> fileImportErrorRepository.persist(batch)
    ).onFailure().recoverWithUni(throwable -> {
        // Silent failure - just log and ignore
        log.error("Unable to persist succeed import row, silently ignored...");
        return Uni.createFrom().voidItem();
    });
});
```

### 2. **Race Conditions**
- Multiple concurrent transactions trying to persist error records
- Database constraint violations or deadlocks
- Parallel streams causing unpredictable execution order

### 3. **Inconsistent Counting**
- Logs showed all 10 failures being processed
- Some batches failed silently during persistence
- Final count in database was inconsistent

## Solution Implemented

### 1. **Sequential Batch Processing**
Replaced `parallelStream()` with sequential processing to avoid race conditions:

```java
// Process batches sequentially to avoid race conditions
for (int i = 0; i < splits.size(); i++) {
    final int batchIndex = i;
    final List<FileImportError> batch = splits.get(i);
    // ... process batch
}
```

### 2. **Retry Mechanism**
Added automatic retry with exponential backoff for transient failures:

```java
.onFailure().retry().withBackOff(Duration.ofMillis(100)).atMost(3)
```

### 3. **Fallback to Individual Record Persistence**
When batch persistence fails after retries, fall back to persisting individual records:

```java
.onFailure().recoverWithUni(throwable -> {
    log.error("Failed to persist error batch {} after retries...", batchIndex + 1);
    return persistIndividualRecords(batch, batchIndex);
});
```

### 4. **Enhanced Logging**
Added comprehensive logging for better debugging:

```java
log.info("Starting to persist {} FileImportError records", fileImportErrors.size());
log.debug("Successfully persisted batch {} with {} error records", batchIndex + 1, batch.size());
log.error("Failed to persist error batch {} after retries. MinRow: {}, MaxRow: {}, BatchSize: {}", ...);
```

### 5. **Individual Record Fallback**
Created a fallback method that attempts to persist records individually when batch fails:

```java
private Uni<Void> persistIndividualRecords(List<FileImportError> batch, int batchIndex) {
    // Attempt to persist each record individually
    // Even if some individual records fail, continue with others
}
```

## Benefits of the Fix

### 1. **Consistency**
- All logged failures will now be persisted to the database
- Eliminates the discrepancy between logs and stored records

### 2. **Reliability**
- Retry mechanism handles transient database issues
- Fallback ensures maximum data preservation

### 3. **Observability**
- Enhanced logging provides better debugging information
- Clear indication when batch vs individual persistence is used

### 4. **Performance**
- Sequential processing eliminates race conditions
- Batch processing is still used when possible for efficiency

## Testing

Created comprehensive unit tests in `FileImportErrorServiceTest.java`:

- Empty list handling
- Successful batch persistence
- Batch failure with retry and fallback
- Large list splitting into batches
- Count operations

## Configuration

The fix uses the existing `MAX_LIMIT` constant from `RequestConstants` for batch sizing. No additional configuration is required.

## Monitoring

The enhanced logging will help monitor:
- Batch success/failure rates
- When fallback to individual persistence is triggered
- Performance impact of the changes

## Future Improvements

1. **Metrics**: Add Micrometer metrics for batch success/failure rates
2. **Alerting**: Set up alerts for persistent batch failures
3. **Configuration**: Make retry attempts and backoff configurable
4. **Database Optimization**: Consider database-specific optimizations for bulk inserts

## Related Files Modified

- `src/main/java/com/tripudiotech/migration/service/FileImportErrorService.java`
- `src/main/java/com/tripudiotech/migration/service/helper/AuthServiceHelper.java`
- `src/test/java/com/tripudiotech/migration/service/FileImportErrorServiceTest.java` (new)
